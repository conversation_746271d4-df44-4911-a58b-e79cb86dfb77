<template>
  <div class="top-apps-up-card">
    <a-row style="margin-left: 1%;padding-top: 1%;">
      <a-col :span="2">
        <img class="top-apps-toux" :src="topAppInfo.iconUrl || logoUrl" @error="handleImageError" alt="">
      </a-col>
      <a-col :span="8">
        <a-row style="font-size: 20px;font-weight: bold;margin-top: 1%;">{{ topAppInfo.nameZh || '王者荣耀' }}</a-row>
        <a-row style="font-size: 14px;margin-top: 2%;">{{ topAppInfo.developerName || '佚名' }}</a-row>
        <a-row style="font-size: 14px;font-weight: lighter;margin-top: 2%;">{{ topAppInfo.ageRating || '12周岁+' }}</a-row>
      </a-col>
      <a-col :span="8" :offset="6">
        <div style="display: flex; align-items: center;">
          <a-select
            v-model:value="searchGameId"
            show-search
            :filter-option="false"
            :not-found-content="searching ? '加载中...' : '无数据'"
            :options="searchGameOptions"
            placeholder="搜索游戏"
            class="top-apps-search-input"
            style="width: 60%; margin-top: 4%;"
            @search="onSearchGame"
            @change="onSelectGame"
            option-label-prop="label"
            @popupScroll="onPopupScroll"
          >
            <template #option="{ value, label, iconUrl }">
              <div style="display: flex; align-items: center;">
                <img :src="iconUrl" style="width: 24px; height: 24px; margin-right: 8px;" v-if="iconUrl" />
                <span>{{ label }}</span>
              </div>
            </template>
          </a-select>
          <a-button type="primary" style="margin-left: 8px; margin-top: 4%;" @click="onSearchBtn">搜索</a-button>
        </div>
      </a-col>
    </a-row>

    <div style="margin-top: 2%;margin-left: 4%;">
      <a-row>
        <a-col :span="8">
          <a-row>
            <a-col :span="24" style="font-weight: bold;font-size: 16px;">类别</a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="font-size: 16px;">{{ topAppInfo.categories || '射击类' }}</a-col>
          </a-row>
        </a-col>

        <a-col :span="8">
          <a-row>
            <a-col :span="24" style="font-weight: bold;font-size: 16px;">价格</a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="font-size: 16px;">{{ topAppInfo.price || '免费' }}</a-col>
          </a-row>
        </a-col>

        <!-- <a-col :span="8">
          <a-row>
            <a-col :span="24" style="font-weight: bold;font-size: 16px;">热门国家/地区</a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="font-size: 16px;">{{ topAppInfo.regionName || '新加坡' }}</a-col>
          </a-row>
        </a-col> -->

      </a-row>
      <a-row style="margin-top: 2%;">

        <a-col :span="8">
          <a-row>
            <a-col :span="24" style="font-weight: bold;font-size: 16px;">来源渠道</a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="font-size: 16px;">{{ topAppInfo.platformId === 'apple' ? 'App Store' : (topAppInfo.platformId === 'google' ? 'Google Play' : topAppInfo.platformId) }}</a-col>
          </a-row>
        </a-col>

      <a-col :span="8">
        <a-row>
          <a-col :span="24" style="font-weight: bold;font-size: 16px;">支持URL</a-col>
        </a-row>
                    <a-row>
            <a-col :span="24" style="font-size: 16px;">{{ topAppInfo.supportUrl || '' }}</a-col>
          </a-row>
        <a-row>
          <a-col :span="24" style="font-size: 16px;">
            <a
              v-if="topAppInfo.developerUrl"
              :href="topAppInfo.developerUrl"
              target="_blank"
              style="color: #29ade6; word-break: break-all;"
            >
              {{ topAppInfo.developerUrl && topAppInfo.developerUrl.length > 32 ? topAppInfo.developerUrl.slice(0, 32) + '...' : topAppInfo.developerUrl || '上海游戏银河有限公司' }}
            </a>
            <template v-else>
              上海游戏银河有限公司
            </template>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="8">
        <a-row>
          <a-col :span="24" style="font-weight: bold;font-size: 16px;">开发者网站</a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="font-size: 16px;">
            <a
              v-if="topAppInfo.developerUrl"
              :href="topAppInfo.developerUrl"
              target="_blank"
              style="color: #29ade6; word-break: break-all;"
            >
              {{ topAppInfo.developerUrl && topAppInfo.developerUrl.length > 32 ? topAppInfo.developerUrl.slice(0, 32) + '...' : topAppInfo.developerUrl || '上海游戏银河有限公司' }}
            </a>
            <template v-else>
              上海游戏银河有限公司
            </template>
          </a-col>
        </a-row>
      </a-col>

      </a-row>
    </div>


  </div>



   <!-- 筛选区 -->
    <div class="top-apps-filter-bar">
      <a-space :size="16" align="center">
        <a-range-picker
          v-model:value="value1"
          :presets="rangePresets"
          @change="onRangeChange"
          style="width: 260px;"
        />
        <a-select
          v-model:value="country_data"
          mode="multiple"
          allowClear
          size="middle"
          placeholder="请选择国家/地区"
          style="width: 200px;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleCountryChange"
        >
          <a-select-option value="all" @click="selectAllCountries">选择全部</a-select-option>
          <a-select-option v-for="country in select_country" :key="country.value" :value="country.value">
            {{ country.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="equipment"
          mode="multiple"
          allowClear
          placeholder="请选择平台"
          style="width: 200px;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleEquipmentChange"
        >
          <a-select-option value="all" @click="selectAllEquipment">选择全部</a-select-option>
          <a-select-option v-for="item in equipment_data" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="selectedGameCategory"
          mode="multiple"
          allowClear
          placeholder="请选择游戏类别"
          style="width: 200px;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleGameCategoryChange"
        >
          <a-select-option value="all" @click="selectAllGames">选择全部</a-select-option>
          <a-select-option v-for="game in games" :key="game.value" :value="game.value">
            {{ game.label }}
          </a-select-option>
        </a-select>
        <a-button type="primary" @click="bijiao">确定</a-button>
      </a-space>
    </div>

    <!-- 游戏列表表格 -->
    <div class="top-apps-table-container">
      <a-table
        :columns="columns"
        :data-source="Publisher_Date"
        :pagination="paginationProp"
        row-key="gameId"
        size="middle"
        bordered
        class="top-apps-custom-table"
      >
        <!-- 仅游戏名称列可点击跳转 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'nameZh'">
            <span
              style="color: #1890ff; cursor: pointer;"
              @click="click_app(record)"
            >
              {{ record.nameZh }}
            </span>
          </template>
          <template v-else-if="column.key === 'percentage_download'">
  <div style="display: flex; align-items: center; gap: 8px;">
    <a-progress
      :percent="Number((record.downloadTimeRate * 100).toFixed(2))"
      size="small"
      :show-info="false"
      stroke-color="#40a9ff"
      style="flex: 1; min-width: 0;"
    />
    <span style="min-width: 48px; text-align: right; font-weight: bold; color: #40a9ff;">
      {{ Number((record.downloadTimeRate * 100).toFixed(2)) }}%
    </span>
  </div>
</template>
<template v-else-if="column.key === 'percentage_income'">
  <div style="display: flex; align-items: center; gap: 8px;">
    <a-progress
      :percent="Number((record.incomeRate * 100).toFixed(2))"
      size="small"
      :show-info="false"
      stroke-color="#ff9c6e"
      style="flex: 1; min-width: 0;"
    />
    <span style="min-width: 48px; text-align: right; font-weight: bold; color: #ff9c6e;">
      {{ Number((record.incomeRate * 100).toFixed(2)) }}%
    </span>
  </div>
</template>
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </template>
        <!-- 空数据显示 -->
        <template #emptyText>
          <div class="top-apps-empty-data-container">
            <i class="top-apps-empty-icon">🔍</i>
            <p>没有找到匹配的数据</p>
            <p class="top-apps-empty-data-tip">请尝试调整筛选条件后再次查询</p>
          </div>
        </template>
      </a-table>
    </div>
</template>

<script lang="ts" name="basic-table-demo" setup>
  import logoUrl from '@/assets/images/default-game.png';
  import { ActionItem, BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import {columns} from './component/From_component.data';
  import {ref,onMounted,h,computed} from 'vue';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import type { CascaderProps,SelectProps } from 'ant-design-vue';
  import { Cascader } from 'ant-design-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { getAppListApi, getAllGenreApi, queryAppByNameApi, getAllCountryApi } from '/@/api/store-information/market-dynamics/top-apps/index';
  import { findGamesByPrefixApi } from '/@/api/public-opinion-monitoring/index';
  // 新增：用于存储表格数据和顶部详情数据
  const Publisher_Date = ref<any[]>([]);
  const topAppInfo = ref<any>({});
  const total = ref(0);
    //分页相关
  const page = ref(1);
  const pageSize = ref(10);
  // 设备选择的数据
      const equipment_data=ref([

      {
        label: 'App Store',
        value: 'apple',
      }, 
      {
        label: 'Google Play',
        value: 'google',
      },
    ])
  // 选择后的设备数据
  const equipment = ref<string[]>([]);

  // 搜索数据
  const Search_content=ref();
  const handleImageError = (e: Event) => {
    if (e.target instanceof HTMLImageElement) {
      e.target.src = logoUrl;
    }
  };
  // 选择设备多选框
  const options: CascaderProps['options'] = equipment_data.value

  // 时间选择框的便捷选项设置
  type RangeValue = [Dayjs, Dayjs];
    const selectedGameCategory = ref<string[]>([]); 
  const rangePresets = ref([
    { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
    { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
    { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
    { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
    { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
    { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
    { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
    { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
  ]);
  // 设置默认值为近一年
  const value1 = ref<RangeValue | null>([dayjs().add(-1, 'year'), dayjs()]);
  // 页面初始化时请求数据
  const fetchAppList = async () => {
    try {
      // 构建请求参数，使用默认时间范围
      const requestData = {
        startTime: value1.value ? value1.value[0].format('YYYY-MM-DD') : dayjs().add(-1, 'year').format('YYYY-MM-DD'),
        endTime: value1.value ? value1.value[1].format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        countryNames: country_data.value.length > 0 ? country_data.value : [],
        platformNames: equipment.value.length > 0 ? equipment.value : [],
        gameCategories: selectedGameCategory.value.length > 0 ? selectedGameCategory.value : [],
        page: page.value,
        pageSize: pageSize.value,
      };

      // console.log('fetchAppList请求参数:', requestData);

      const res = await getAppListApi(requestData);

      // 根据实际返回格式处理数据
      if (res && res.games) {
        Publisher_Date.value = res.games || [];
        total.value = res.totalGames || 0;

        const totalDownloads = res.totalDownloads || 1; // 避免除零
        const totalRevenues = res.totalRevenues || 1; // 避免除零

        // 为每个游戏添加额外信息用于显示
        Publisher_Date.value = Publisher_Date.value.map((game, index) => ({
          ...game,
          ranking: index + 1 + (page.value - 1) * pageSize.value, // 计算全局排名
          nameZh: game.gameName, // 映射游戏名称
          gameId: game.gameId, // 使用新接口的gameId字段
          downloadTimeRate: (game.downloads || 0) / totalDownloads, // 计算下载量占比（用于进度条显示）
          incomeRate: (game.revenues || 0) / totalRevenues, // 计算收入占比（用于进度条显示）
          percentage_download: (game.downloads || 0) / totalDownloads, // 百分比列数据
          percentage_income: (game.revenues || 0) / totalRevenues, // 百分比列数据
        }));

        if (Publisher_Date.value.length > 0) {
          const firstGame = Publisher_Date.value[0];
          topAppInfo.value = {
            ...firstGame,
            nameZh: firstGame.gameName || firstGame.nameZh, // 确保游戏名称字段正确
            iconUrl: firstGame.icon || '', // 使用新接口的icon字段
            developerName: firstGame.publisher || '', // 使用新接口的publisher字段
            ageRating: firstGame.contentRating || '12周岁+', // 使用新接口的contentRating字段
            categories: firstGame.genre || '', // 使用新接口的genre字段
            price: firstGame.price === 0 ? '免费' : `$${firstGame.price}`, // 根据价格显示
            platformId: firstGame.storeType || 'apple', // 使用新接口的storeType字段
            supportUrl: firstGame.supportUrl || '',
            developerUrl: firstGame.websiteUrl || '',
          };
        }

      }
    } catch (e) {
      // console.error('获取应用列表失败:', e);
      // console.error('错误详情:', (e as Error).message || e);
      Publisher_Date.value = [];
      topAppInfo.value = {};
      total.value = 0;
    }
  };

  onMounted(() => {
    fetchAppList();
    fetchGameGenres();
    fetchGameOptions();
    fetchCountries();
  });

  const paginationProp = computed(() => ({
    showSizeChanger: false,
    showQuickJumper: true,
    pageSize: pageSize.value,
    current: page.value,
    total: total.value,
    showTotal: (total) => `总 ${total} 条`,
    onChange: pageChange,
    onShowSizeChange: pageSizeChange,
  }));

  function pageChange(p, pz) {
    // console.log('分页变化:', { 当前页: p, 每页大小: pz });
    page.value = p;
    pageSize.value = pz;
    // 如果有筛选条件，使用筛选后的数据请求，否则使用默认请求
    if (country_data.value.length > 0 || equipment.value.length > 0 || selectedGameCategory.value.length > 0) {
      bijiao(); // 使用筛选条件重新请求
    } else {
      fetchAppList(); // 使用默认条件请求
    }
  }
  function pageSizeChange(current, size) {
    pageSize.value = size;
    // 如果有筛选条件，使用筛选后的数据请求，否则使用默认请求
    if (country_data.value.length > 0 || equipment.value.length > 0 || selectedGameCategory.value.length > 0) {
      bijiao(); // 使用筛选条件重新请求
    } else {
      fetchAppList(); // 使用默认条件请求
    }
  }
  const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
    if (dates) {
      // console.log('From: ', dates[0], ', to: ', dates[1]);
      // console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
    } else {
      // console.log('Clear');
    }
  };
  // 国家地区的选择
  const select_country = ref<{ value: string; label: string }[]>([]);

  const country_1 = ref<SelectProps['options']>(select_country.value);
  const country_data = ref<string[]>([]);

    const games = ref<{ value: string; label: string }[]>([]);
      const fetchGameGenres = async () => {
        try {
          const res = await getAllGenreApi();
          games.value = [
            ...(res || []).map((item: any) => ({
              value: item.value,
              label: item.value,
            })),
          ];
        } catch (e) {
        }
      };

      // 获取国家数据
      const fetchCountries = async () => {
        try {
          const res = await getAllCountryApi();

          if (res && Array.isArray(res)) {
            select_country.value = res
              .filter((item: any) => {
                return item &&
                       typeof item === 'object' &&
                       item.value &&
                       typeof item.value === 'string' &&
                       item.value.trim() !== '' &&
                       item.value !== '5' &&
                       item.value !== '6'; // 过滤掉数字ID
              })
              .map((item: any) => ({
                value: item.value.trim(),
                label: item.value.trim(),
              }));
          } else {
            throw new Error('API返回数据格式不正确');
          }
        } catch (e) {
          // 如果API失败，使用默认数据
          select_country.value = [
            { value: '菲律宾', label: '菲律宾' },
            { value: '柬埔寨', label: '柬埔寨' },
            { value: '马来西亚', label: '马来西亚' },
            { value: '泰国', label: '泰国' },
            { value: '文莱', label: '文莱' },
            { value: '新加坡', label: '新加坡' },
            { value: '印度尼西亚', label: '印度尼西亚' },
            { value: '越南', label: '越南' },
            { value: '缅甸', label: '缅甸' },
            { value: '中国台湾', label: '中国台湾' },
            { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
          ];
        }
      };
  const game_1 = ref<{ value: string; label: string }[]>([]);
  const game_data = ref([]);
  
// 搜索游戏下拉框相关
import { useRouter } from 'vue-router';
const router = useRouter();
const searchGameId = ref('');
const searchGameOptions = ref<any[]>([]);
const searching = ref(false);
const searchGameTotal = ref(0);
const searchGameKeyword = ref('');

const onSearchGame = async (value: string) => {
  searchGameKeyword.value = value;
  await fetchGameOptions();
};
const onPopupScroll = async (e: Event) => {
  // 前缀搜索通常一次返回所有结果，不需要分页加载
  // 保留函数以避免模板报错，但不执行任何操作
};
const fetchGameOptions = async (append = false) => {
  searching.value = true;
  try {
    // 如果没有搜索关键词，传递空前缀获取默认游戏
    const searchPrefix = searchGameKeyword.value ? searchGameKeyword.value.trim() : '';

    // 先尝试新接口
    try {
      const res = await findGamesByPrefixApi({
        prefix: searchPrefix,
      });

      // 根据新接口的返回格式处理数据
      const games = res.records || [];
      searchGameTotal.value = games.length;

      const options = games.map((item: any) => ({
        label: item.nameZh || item.gameName || item.name, // 优先使用 nameZh
        value: item.appId || item.gameId || item.id, // 优先使用 appId
        iconUrl: item.iconUrl || item.icon || '', // 适配不同的图标字段名
      }));

      searchGameOptions.value = options;
    } catch (prefixError) {
      // console.error('findGamesByPrefixApi调用失败，尝试使用原接口:', prefixError);

      // 如果新接口失败，回退到原接口
      const res = await queryAppByNameApi({
        pageNo: 1,
        pageSize: 20,
        platformName: searchGameKeyword.value.trim(),
      });

      const records = res.records || [];
      searchGameTotal.value = res.total || 0;
      const options = records.map((item: any) => ({
        label: item.nameZh,
        value: item.gameId,
        iconUrl: item.iconUrl,
      }));

      searchGameOptions.value = options;
    }
  } catch (error) {
    // console.error('搜索游戏失败:', error);
    searchGameOptions.value = [];
    searchGameTotal.value = 0;
  } finally {
    searching.value = false;
  }
};
const onSelectGame = (gameId: string) => {
  searchGameId.value = gameId;
};
const onSearchBtn = () => {
  if (!searchGameId.value) return;
  router.push({
    name: 'applicationDetails',
    query: {
      appId: searchGameId.value,
    },
  });
};

// BasicTable注册，分页配置
const { tableContext } = useListPage({
  designScope: 'basic-table-demo',
  tableProps: {
    title: '',
    dataSource: Publisher_Date,
    columns: columns,
    showTableSetting: false,
    showActionColumn: false,
    rowSelection: { type: 'checkbox' }
  },
});
  const [registerTable, { reload }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;
  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  function handleEdit(record) {
    // console.log(record);
    // console.log(selectedRows.value);
    // console.log(selectedRowKeys.value);
  }

// 标签溢出处理函数
const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { class: 'ellipsis-tag' }, '...');
};

// 国家选择相关函数
const selectAllCountries = () => {
  country_data.value = select_country.value.map(item => item.value);
};

const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    country_data.value = select_country.value.map(item => item.value);
  }
};

// 平台选择相关函数
const selectAllEquipment = () => {
  equipment.value = equipment_data.value.map(item => item.value);
};

const handleEquipmentChange = (value: string[]) => {
  if (value.includes('all')) {
    equipment.value = equipment_data.value.map(item => item.value);
  }
};

// 游戏类别选择相关函数
const selectAllGames = () => {
  selectedGameCategory.value = games.value.map(item => item.value);
};

const handleGameCategoryChange = (value: string[]) => {
  if (value.includes('all')) {
    selectedGameCategory.value = games.value.map(item => item.value);
  }
};

// 修改比较函数
function bijiao() {

  // 构建请求参数
  let startTime = '';
  let endTime = '';
  if (value1 && value1.value && value1.value.length === 2) {
    startTime = value1.value[0].format('YYYY-MM-DD');
    endTime = value1.value[1].format('YYYY-MM-DD');
  }

  const requestData = {
    startTime,
    endTime,
    countryNames: country_data.value || [],
    platformNames: equipment.value || [],
    gameCategories: selectedGameCategory.value || [],
    page: page.value,
    pageSize: pageSize.value,
  };

  // 请求接口
  getAppListApi(requestData).then(res => {
    if (res && res.games) {
      Publisher_Date.value = res.games || [];
      total.value = res.totalGames || 0;

      const totalDownloads = res.totalDownloads || 1; // 避免除零
      const totalRevenues = res.totalRevenues || 1; // 避免除零

      // 为每个游戏添加额外信息用于显示
      Publisher_Date.value = Publisher_Date.value.map((game, index) => ({
        ...game,
        ranking: index + 1 + (page.value - 1) * pageSize.value,
        nameZh: game.gameName,
        gameId: game.gameId, // 使用新接口的gameId字段
        downloadTimeRate: (game.downloads || 0) / totalDownloads, // 计算下载量占比（用于进度条显示）
        incomeRate: (game.revenues || 0) / totalRevenues, // 计算收入占比（用于进度条显示）
        percentage_download: (game.downloads || 0) / totalDownloads, // 百分比列数据
        percentage_income: (game.revenues || 0) / totalRevenues, // 百分比列数据
      }));

      if (Publisher_Date.value.length > 0) {
        const firstGame = Publisher_Date.value[0];
        topAppInfo.value = {
          ...firstGame,
          nameZh: firstGame.gameName || firstGame.nameZh, // 确保游戏名称字段正确
          iconUrl: firstGame.icon || '', // 使用新接口的icon字段
          developerName: firstGame.publisher || '', // 使用新接口的publisher字段
          ageRating: firstGame.contentRating || '12周岁+', // 使用新接口的contentRating字段
          categories: firstGame.genre || '', // 使用新接口的genre字段
          price: firstGame.price === 0 ? '免费' : `$${firstGame.price}`, // 根据价格显示
          platformId: firstGame.storeType || 'apple', // 使用新接口的storeType字段
          supportUrl: firstGame.supportUrl || '',
          developerUrl: firstGame.websiteUrl || '',
        };
      }
    }
  }).catch(error => {
    // console.error('获取应用列表失败:', error);
    Publisher_Date.value = [];
    topAppInfo.value = {};
    total.value = 0;
  });
}

  function click_app(record){
    router.push({
      name: 'applicationDetails',
      query: {
        appId: record.appId || record.gameId,
      },
    });

  }
</script>

<style scoped>
.top-apps-up-card {
  margin: 24px 2% 0 10px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 24px;
}

.top-apps-filter-bar {
  margin: 24px 0 12px 10px;
  display: flex;
  align-items: center;
}

.top-apps-table-container {
  margin: 24px 2% 0 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 24px;
}

.top-apps-custom-table {
  width: 100%;
  font-size: 14px;
}

/* 表头样式 */
:deep(.top-apps-custom-table .ant-table-thead > tr > th) {
  background-color: #c2e8f8;
  color: #333;
  font-weight: bold;
  text-align: center;
  padding: 12px 16px;
}

/* 表格单元格样式 */
:deep(.top-apps-custom-table .ant-table-tbody > tr > td) {
  padding: 12px 16px;
  text-align: center;
  color: #666;
  vertical-align: middle;
  border-bottom: 1px solid #eee;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-variant-numeric: tabular-nums;
}

/* 斑马纹样式 */
:deep(.top-apps-custom-table .ant-table-tbody > tr:nth-child(odd)) {
  background-color: #ffffff;
}
:deep(.top-apps-custom-table .ant-table-tbody > tr:nth-child(even)) {
  background-color: #dcf2fb;
}

/* 悬停样式 */
:deep(.top-apps-custom-table .ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}

/* 标题链接样式 */
.top-apps-title-link {
  color: #018ffb;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.top-apps-title-link:hover {
  background-color: rgba(1, 143, 251, 0.1);
  color: #0170c9;
}

.top-apps-empty-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.top-apps-empty-icon {
  font-size: 32px;
  color: #ccc;
  margin-bottom: 10px;
}

.top-apps-empty-data-container p {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.top-apps-empty-data-tip {
  font-size: 14px;
  color: #999;
}

.top-apps-toux {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e6e6e6;
  margin-top: 8px;
  margin-left: 8px;
}

/* 搜索框样式 */
.top-apps-search-input {
  width: 60%;
  margin-top: 4%;
}

/* 进度条容器样式 */
.top-apps-progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 进度条样式 */
:deep(.ant-progress) {
  flex: 1;
  min-width: 0;
}

/* 进度条数值样式 */
.top-apps-progress-value {
  min-width: 48px;
  text-align: right;
  font-weight: bold;
}

/* 下载进度条颜色 */
:deep(.download-progress .ant-progress-bg) {
  background-color: #40a9ff !important;
}

/* 收入进度条颜色 */
:deep(.income-progress .ant-progress-bg) {
  background-color: #ff9c6e !important;
}

/* 链接样式 */
a {
  color: #29ade6;
  word-break: break-all;
  text-decoration: none;
}

a:hover {
  color: #1890ff;
}

/* 表格内容样式 */
:deep(.ant-table-cell) {
  font-size: 14px;
  line-height: 1.5;
}

/* 分页器样式 */
:deep(.ant-pagination) {
  margin-top: 16px;
  text-align: right;
}

/* 选择器样式 */
:deep(.ant-select) {
  width: 200px;
}

/* 日期选择器样式 */
:deep(.ant-picker) {
  width: 260px;
}

/* 按钮样式 */
:deep(.ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

</style>